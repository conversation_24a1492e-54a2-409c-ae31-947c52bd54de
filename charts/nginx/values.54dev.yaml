# Default values for nginx.
replicaCount: 1
namespace: 54dev

image:
  repository: swr.ru-moscow-1.hc.sbercloud.ru/tkp2/sdbp-nginx
  tag: latest
  pullPolicy: Always

# Ingress configuration for virtual hosts
ingress:
  enabled: false
  className: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
  hosts:
    - host: "admin.sdbp.dev"
      paths:
        - path: /
          pathType: Prefix
    - host: "api.sdbp.dev"
      paths:
        - path: /
          pathType: Prefix
    - host: "api-mobile.sdbp.dev"
      paths:
        - path: /
          pathType: Prefix
    - host: "api-mobile.sdbp.an.dev.xz-lab.ru"
      paths:
        - path: /
          pathType: Prefix
    - host: "api-geo.sdbp.dev"
      paths:
        - path: /
          pathType: Prefix
    - host: "api-fin.sdbp.dev"
      paths:
        - path: /
          pathType: Prefix

# Gateway configuration for Istio
gateway:
  enabled: false
  namespace: "istio-ingressgateway"
  port:
    name: "http"
    number: 80
    protocol: "HTTP"
  tls: {}

# VirtualService configuration for Istio
virtualService:
  enabled: false
  gateway: "istio-ingressgateway/54dev-gateway"  # fallback when gateway.enabled is false
  destination:
    host: "sdbp-nginx.54dev.svc.cluster.local"
    port: 80

# Server names configuration for 54dev environment
servers:
  admin:
    serverName: "admin.sdbp.54dev-kube.tkp2.prod"
  api:
    serverName: "api.sdbp.54dev-kube.tkp2.prod"
  apiMobile:
    serverName: "api-mobile.sdbp.54dev-kube.tkp2.prod api-mobile.sdbp.an.54dev-kube.tkp2.prod"
  apiFin:
    serverName: "api-fin.sdbp.54dev-kube.tkp2.prod"

resources:
  limits:
    cpu: 500m
    memory: 512Mi
  requests:
    cpu: 50m
    memory: 64Mi
